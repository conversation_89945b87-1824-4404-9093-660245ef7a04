import React, { memo, useCallback, useEffect, useMemo } from "react";
import { useErrorHandler } from "../hooks/useErrorHandler";
import { useAudioStore } from "../stores/audioStore";
import { useColorStore } from "../stores/colorStore";
import { AUDIO_STATES } from "../utils/constants";
import ColorPicker from "./ColorPicker";
import { InlineLoader } from "./LoadingSpinner";
import RichTextEditor from "./RichTextEditor";
import "./ToolsPanel.css";

const ToolsPanel = memo(
  ({ activeTextSize, onSetTextSize, currentTopic, onPageChange }) => {
    // State and store hooks
    const [noteText, setNoteText] = React.useState("");
    const [noteKey, setNoteKey] = React.useState(""); // Add key to force re-render
    const { selectedColor, setSelectedColor } = useColorStore();
    const { isHighlightMode, setHighlightMode } = useHighlightStore();
    const { addError } = useErrorHandler();
    const {
      isPlaying,
      playlist,
      trackIndex,
      audioState,
      error,
      play,
      pause,
      nextTrack,
      prevTrack,
      hasNextTrack,
      hasPrevTrack,
      setTrackIndex,
    } = useAudioStore();

    // Enhanced helper function to generate unique storage key for each page
    const generateStorageKey = useCallback((topic) => {
      if (!topic) return null;

      // Get the base topic ID
      const topicId = topic.realTopicId || topic.id;

      // Get current page information
      let pageIdentifier;

      if (topic.originalStructure && topic.originalStructure.pageId) {
        // If we have original structure with pageId, use it
        pageIdentifier = topic.originalStructure.pageId;
      } else if (topic.currentPageIndex !== undefined) {
        // Use currentPageIndex if available
        pageIdentifier = `page-${topic.currentPageIndex}`;
      } else if (topic.currentPage) {
        // Use currentPage if available
        pageIdentifier = `page-${topic.currentPage}`;
      } else {
        // Fallback to page 1
        pageIdentifier = "page-0";
      }

      // Create a unique key that includes subtopic info if available
      const subTopicId =
        topic.originalStructure?.subTopicId || topic.subTopicId || "";

      const storageKey = subTopicId
        ? `notes_${topicId}_${subTopicId}_${pageIdentifier}`
        : `notes_${topicId}_${pageIdentifier}`;

      return storageKey;
    }, []);

    // Notes persistence logic - Load notes when page changes
    useEffect(() => {
      if (currentTopic) {
        const storageKey = generateStorageKey(currentTopic);
        if (storageKey) {
          const savedNotes = localStorage.getItem(storageKey) || "";
          setNoteText(savedNotes);
          setNoteKey(storageKey); // Update key to force RichTextEditor re-render
        }
      } else {
        // Clear notes if no topic
        setNoteText("");
        setNoteKey("");
      }
    }, [currentTopic, generateStorageKey]);

    // Save notes when noteText changes
    useEffect(() => {
      if (currentTopic && noteText !== undefined) {
        const storageKey = generateStorageKey(currentTopic);
        if (storageKey) {
          if (noteText.trim()) {
            localStorage.setItem(storageKey, noteText);
          } else {
            localStorage.removeItem(storageKey);
          }
        }
      }
    }, [noteText, currentTopic, generateStorageKey]);

    // Handle note text changes
    const handleNoteChange = useCallback((e) => {
      setNoteText(e.target.value);
    }, []);

    // Memoized calculations and state derivations
    const currentTrack = playlist[trackIndex] || {};
    const hasAudio = !!currentTrack?.audioSrc;

    const { currentPage, totalPages } = useMemo(() => {
      if (!currentTopic?.pages || !currentTopic.originalStructure) {
        return { currentPage: 1, totalPages: 1 };
      }
      const currentPageIndex = currentTopic.currentPageIndex || 0;
      return {
        currentPage: currentPageIndex + 1,
        totalPages: currentTopic.originalStructure.totalPages,
      };
    }, [currentTopic]);

    // --- FULL HANDLER FUNCTIONS ---

    const handlePageClick = useCallback(
      (pageNumber) => {
        if (currentTopic && onPageChange) {
          onPageChange(currentTopic.id, pageNumber);
        }
      },
      [currentTopic, onPageChange]
    );

    const handleNextPage = useCallback(() => {
      if (currentPage < totalPages) {
        handlePageClick(currentPage + 1);
      }
    }, [currentPage, totalPages, handlePageClick]);

    const handleTextSizeChange = useCallback(
      (size) => {
        onSetTextSize(size);
      },
      [onSetTextSize]
    );

    const handleColorSelect = useCallback(
      (color) => {
        setSelectedColor(color);
      },
      [setSelectedColor]
    );

    const handleAudioControl = useCallback(
      async (action) => {
        try {
          if (action === "play") await play();
          else if (action === "pause") pause();
          else if (action === "next") nextTrack();
          else if (action === "prev") prevTrack();
        } catch (err) {
          addError("Audio control failed");
        }
      },
      [play, pause, nextTrack, prevTrack, addError]
    );

    const handleTrackSelect = useCallback(
      (index) => {
        setTrackIndex(index);
      },
      [setTrackIndex]
    );

    // Removed bookmark functionality

    return (
      <aside className="tools-panel-sidebar">
        {/* 1. Page Section */}
        <div className="tool-section">
          <div className="section-header">
            <span className="section-title">Page:</span>
          </div>
          <div className="page-controls-wrapper">
            <div className="pagination-controls">
              <button
                className="pagination-nav-btn"
                onClick={() => handlePageClick(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                {"<"}
              </button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (pageNum) => (
                  <button
                    key={pageNum}
                    className={`pagination-btn ${pageNum === currentPage ? "active" : ""
                      }`}
                    onClick={() => handlePageClick(pageNum)}
                  >
                    {pageNum}
                  </button>
                )
              )}
              <button
                className="pagination-nav-btn"
                onClick={handleNextPage}
                disabled={currentPage >= totalPages}
              >
                {">"}
              </button>
            </div>
            {/* Bookmark button removed */}
          </div>
        </div>

        {/* 2. Text Size Section */}
        <div className="tool-section">
          <div className="section-header">
            <span className="section-title">Text size:</span>
          </div>
          <div className="text-size-controls">
            <button
              className={`text-size-btn ${activeTextSize === "small" ? "active" : ""
                }`}
              onClick={() => handleTextSizeChange("small")}
            >
              Small
            </button>
            <button
              className={`text-size-btn ${activeTextSize === "normal" ? "active" : ""
                }`}
              onClick={() => handleTextSizeChange("normal")}
            >
              Normal
            </button>
            <button
              className={`text-size-btn ${activeTextSize === "large" ? "active" : ""
                }`}
              onClick={() => handleTextSizeChange("large")}
            >
              Large
            </button>
          </div>
        </div>

        {/* 3. Now Playing Section */}
        <div className="tool-section">
          <div className="section-header">
            <span className="section-title">Now playing:</span>
          </div>

          <div className="media-player">
            <div className="media-info">
              <span className="media-title">
                {hasAudio ? currentTrack.title : "Rectilinear Motion"}
              </span>
            </div>

            <div className="media-controls">
              {/* Previous Button */}
              <button
                onClick={() => handleAudioControl("prev")}
                className="media-control-btn"
                disabled={
                  !hasPrevTrack() || audioState === AUDIO_STATES.LOADING
                }
                title="Previous"
              >
                <span className="control-icon" role="img" aria-label="Previous">
                  ⏮
                </span>
              </button>

              {/* Play / Pause Button */}
              <button
                onClick={() => handleAudioControl(isPlaying ? "pause" : "play")}
                className="media-control-btn play-pause"
                disabled={!hasAudio}
                title={isPlaying ? "Pause" : "Play"}
              >
                {audioState === AUDIO_STATES.LOADING ? (
                  <InlineLoader size="small" />
                ) : (
                  <span
                    className="control-icon"
                    role="img"
                    aria-label={isPlaying ? "Pause" : "Play"}
                  >
                    {isPlaying ? "⏸" : "▶"}
                  </span>
                )}
              </button>

              {/* Next Button */}
              <button
                onClick={() => handleAudioControl("next")}
                className="media-control-btn"
                disabled={
                  !hasNextTrack() || audioState === AUDIO_STATES.LOADING
                }
                title="Next"
              >
                <span className="control-icon" role="img" aria-label="Next">
                  ⏭
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* 4. Audio List Section */}

        {/* <div className="section-header">
          <span className="section-title">Audio List:</span>
        </div>
        {playlist.length > 0 && (
          <div className="tool-section">
            <div className="audio-list">
              {playlist.map((track, index) => (
                <div
                  key={index}
                  className={`audio-item ${
                    index === trackIndex ? "active" : ""
                  }`}
                  onClick={() => handleTrackSelect(index)}
                >
                  <span className="audio-item-title">
                    {track.title || `Audio ${index + 1}`}
                  </span>
                  {index === trackIndex && isPlaying && (
                    <div className="audio-playing-indicator">
                      <div className="audio-wave">
                        <div className="wave-bar"></div>
                        <div className="wave-bar"></div>
                        <div className="wave-bar"></div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )} */}

        {/* 5. Color Picker Section with Separator */}
        <div className="tool-section annotate-section has-separator">
          <div className="section-header">
            <span className="section-title">PDF Highlighting:</span>
          </div>
          <div className="annotate-controls">
            <div className="highlight-mode-toggle">
              <button
                className={`highlight-mode-btn ${isHighlightMode ? 'active' : ''}`}
                onClick={() => setHighlightMode(!isHighlightMode)}
                title={isHighlightMode ? 'Exit highlight mode' : 'Enter highlight mode'}
              >
                {isHighlightMode ? '🔍 Highlighting Enabled' : '✏️ Enable Highlighting'}
              </button>
            </div>
            <div className="color-picker-section">
              <span className="color-picker-label">Highlight Color:</span>
              <ColorPicker
                selectedColor={selectedColor}
                onColorSelect={handleColorSelect}
                disabled={false}
              />
            </div>
          </div>
        </div>

        <hr className="tools-separator" />

        {/* 6. Notes Section */}
        <div className="tool-section notes-section">
          <RichTextEditor
            key={noteKey} // Force re-render when page changes
            value={noteText}
            onChange={handleNoteChange}
            placeholder="Write your notes here...."
          />
        </div>
      </aside>
    );
  }
);

ToolsPanel.displayName = "ToolsPanel";
export default ToolsPanel;
