import React, { memo, useCallback, useMemo } from "react";
import { useErrorHandler } from "../hooks/useErrorHandler";
import { useHighlightStore } from "../stores/highlightStore";
import { useColorStore } from "../stores/colorStore";
import LoadingSpinner from "./LoadingSpinner";
import PDFViewer from "./PDFViewer";
import "./MainContent.css";

const MainContent = memo(({ topic, textSize }) => {
  // Error handling for component-level errors
  const { addError } = useErrorHandler();

  // Check if content is PDF
  const isPDFContent = useMemo(() => {
    return topic?.content?.body?.some(element => element.type === "pdf") ||
      topic?.pdfUrl;
  }, [topic?.content?.body, topic?.pdfUrl]);

  // Get PDF URL
  const pdfUrl = useMemo(() => {
    if (topic?.pdfUrl) return topic.pdfUrl;

    const pdfContent = topic?.content?.body?.find(element => element.type === "pdf");
    return pdfContent?.pdfUrl;
  }, [topic?.pdfUrl, topic?.content?.body]);

  // Content processing - simplified without block processing
  const getContentHtml = useCallback(() => {
    if (!topic?.content?.body || topic.content.body.length === 0) {
      return "";
    }

    try {
      // Simple content processing without block IDs
      return topic.content.body
        .map((element) => {
          switch (element.type) {
            case "html":
              return element.content || "";
            case "paragraph":
              return `<p>${element.text || ""}</p>`;
            case "pdf":
              return ""; // PDFs are handled separately
            default:
              return "";
          }
        })
        .join("");
    } catch (error) {
      console.error("Failed to process content:", error);
      addError("Failed to load content properly");
      return "<p>Error loading content</p>";
    }
  }, [topic?.content?.body, addError]);

  // Memoize the content HTML to prevent unnecessary re-renders
  const contentHtml = useMemo(() => getContentHtml(), [getContentHtml]);

  if (!topic) {
    return (
      <main className="main-content-panel">
        <LoadingSpinner size="large" message="Loading content..." />
      </main>
    );
  }

  return (
    <main className={`main-content-panel text-size-${textSize}`}>
      <article className="lesson-content">
        <h1>{topic.content?.heading || topic.title}</h1>

        {/* Render PDF content or HTML content */}
        <div className="lesson-content-body">
          {isPDFContent && pdfUrl ? (
            <PDFViewer
              pdfUrl={pdfUrl}
              pageId={topic.id}
              topicId={topic.realTopicId || topic.id}
              textSize={textSize}
            />
          ) : contentHtml ? (
            <div
              className="html-content"
              dangerouslySetInnerHTML={{ __html: contentHtml }}
            />
          ) : (
            <div className="no-content">
              <p>No content available for this topic.</p>
            </div>
          )}
        </div>
      </article>
    </main>
  );
});

// Display name for debugging
MainContent.displayName = "MainContent";

export default MainContent;
