import React, { memo, useCallback, useMemo } from "react";
import { useError<PERSON><PERSON><PERSON> } from "../hooks/useErrorHandler";
import LoadingSpinner from "./LoadingSpinner";
import "./MainContent.css";

const MainContent = memo(({ topic, textSize }) => {
  // Error handling for component-level errors
  const { addError } = useErrorHandler();

  // Content processing - simplified without block processing
  const getContentHtml = useCallback(() => {
    if (!topic?.content?.body || topic.content.body.length === 0) {
      return "";
    }

    try {
      // Simple content processing without block IDs
      return topic.content.body
        .map((element) => {
          switch (element.type) {
            case "html":
              return element.content || "";
            case "paragraph":
              return `<p>${element.text || ""}</p>`;
            default:
              return "";
          }
        })
        .join("");
    } catch (error) {
      console.error("Failed to process content:", error);
      addError("Failed to load content properly");
      return "<p>Error loading content</p>";
    }
  }, [topic?.content?.body, addError]);

  // Memoize the content HTML to prevent unnecessary re-renders
  const contentHtml = useMemo(() => getContentHtml(), [getContentHtml]);

  if (!topic) {
    return (
      <main className="main-content-panel">
        <LoadingSpinner size="large" message="Loading content..." />
      </main>
    );
  }

  return (
    <main className={`main-content-panel text-size-${textSize}`}>
      <article className="lesson-content">
        <h1>{topic.content.heading}</h1>

        {/* Render content using simple HTML */}
        <div className="lesson-content-body">
          {contentHtml ? (
            <div
              className="html-content"
              dangerouslySetInnerHTML={{ __html: contentHtml }}
            />
          ) : (
            <div className="no-content">
              <p>No content available for this topic.</p>
            </div>
          )}
        </div>
      </article>
    </main>
  );
});

// Display name for debugging
MainContent.displayName = "MainContent";

export default MainContent;
