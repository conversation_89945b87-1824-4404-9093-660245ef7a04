/* PDF Viewer Styles */
.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
}

.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.pdf-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pdf-navigation button {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.pdf-navigation button:hover:not(:disabled) {
  background-color: #357abd;
}

.pdf-navigation button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  min-width: 120px;
  text-align: center;
}

.pdf-zoom {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pdf-zoom button {
  padding: 6px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.pdf-zoom button:hover {
  background-color: #5a6268;
}

.zoom-level {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  min-width: 50px;
  text-align: center;
}

.highlight-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.highlight-toggle {
  padding: 8px 16px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.highlight-toggle:hover {
  background-color: #218838;
}

.highlight-toggle.active {
  background-color: #dc3545;
}

.highlight-toggle.active:hover {
  background-color: #c82333;
}

/* PDF Document Container */
.pdf-document-container {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
  background-color: #f8f9fa;
}

.pdf-page-wrapper {
  position: relative;
  display: inline-block;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
}

/* React-PDF specific styles */
.react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.react-pdf__Page {
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.react-pdf__Page__textContent {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.react-pdf__Page__textContent span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

/* Highlights */
.pdf-highlights-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.pdf-highlights-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.pdf-highlight {
  border-radius: 2px;
  transition: opacity 0.2s ease;
  cursor: pointer;
  pointer-events: auto;
}

.pdf-highlight:hover {
  opacity: 0.8;
}

/* Loading and Error States */
.pdf-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
}

.pdf-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #dc3545;
  text-align: center;
}

.pdf-error button {
  margin-top: 12px;
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.pdf-error button:hover {
  background-color: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pdf-controls {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }

  .pdf-navigation,
  .pdf-zoom,
  .highlight-controls {
    justify-content: center;
  }

  .pdf-document-container {
    padding: 10px;
  }
}

/* Text Selection Styling */
.pdf-document-container ::selection {
  background-color: rgba(74, 144, 226, 0.3);
}

.pdf-document-container ::-moz-selection {
  background-color: rgba(74, 144, 226, 0.3);
}

/* Accessibility */
.pdf-controls button:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pdf-highlight {
    border: 2px solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .pdf-highlight,
  .pdf-navigation button,
  .pdf-zoom button,
  .highlight-toggle {
    transition: none;
  }
}
