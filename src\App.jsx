import React, { useCallback, useEffect, useRef, useState } from "react";
import "./App.css";
import CourseStructure from "./components/CourseStructure";
import ErrorBoundary from "./components/ErrorBoundary";
import MainContent from "./components/MainContent";
import ToolsPanel from "./components/ToolsPanel";
import { useAudioStore } from "./stores/audioStore";

import { ToastContainer } from "./components/Toast";
import { useErrorHandler } from "./hooks/useErrorHandler";
import { useLocalStorage } from "./hooks/useLocalStorage";
import { STORAGE_KEYS, TEXT_SIZES } from "./utils/constants";

const mockCourse = {
  id: "1",
  title: "Physics - Mechanics",
  topics: [
    {
      id: "dynamics",
      title: "Dynamics",
      currentPage: 1,
      expanded: true,
      subTopics: [
        {
          id: "rectilinear-motion",
          title: "Rectilinear motion",
          currentPage: 1,
          pages: [
            {
              id: "page-1",
              pageNumber: 1,
              title: "Kinematics of Particle",
              audioUrls: ["/audio1.mp3", "/audio4.mp3", "/audio2.mp3"],
              contents: [
                {
                  id: "content-1",
                  type: "html",
                  order: 1,
                  content: `<h3>Test Kinematics of Particle</h3>
                                    <p>It is the study of bodies in motion, it is  divided into two parts</p>
                                    <p><strong>1. Kinematics</strong> (study of geometry of motion) -</p>
                                    <p>It is the study of relation between displacement (s), velocity (v), acceleration (a) and time (t).</p>
                                    <p><strong>2. Kinetics</strong> -</p>
                                    <p>It is the study of relation between force (f), mass (m), displacement (s), velocity (v), acceleration (a) and time (t).</p>

                                    <p><strong>Types of motion based on geometry</strong> -</p>
                                    <p><strong>1. Translation</strong> -</p>
                                    <p>During the motion of translation, orientation of a body does not change. Translation is of two types :</p>
                                    <ul>
                                        <li><strong>Rectilinear translation.</strong></li>
                                        <li><strong>Curvilinear translation.</strong></li>
                                    </ul>

                                    <div class="diagram-container" style="display: flex; gap: 64px; margin: 40px 0;">
                                        <img src="/image1.png" alt="Physics Diagram 1 - Rectilinear Translation Illustration" class="diagram-image">
                                        
                                    </div>

                                    <p><strong>2. Rotation</strong> -</p>

                                    <p>During the motion of rotation, <span>all the particles will move along concentric circles</span>.</p>
                                    <div class="diagram-container" style="display:  margin: 40px 0;">
                                    <img src="/image3.png" alt="Physics Diagram 3 - Pure Rotation" class="diagram-image">
                                      </div>
                                     <p><strong>3.1. Pure Rotation - </strong>
                                      <p>When motion cannot be categorized as pure translational or pure rotational, it is called as general plane motion.</p>
                                      <div class="diagram-container" style="display:  margin: 40px 0;">
                                      <img src="/image4.png" alt="Physics Diagram 4 - General Plane Motion" class="diagram-image">
                                      </div>
                                      `,
                },
              ],
            },
            {
              id: "page-2",
              pageNumber: 2,
              title: "Types of Motion",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-2",
                  type: "text",
                  order: 1,
                  content: `<h3>Types of motion based on acceleration</h3>

<p><strong>Motion with zero acceleration (constant velocity)</strong> -<br>
Displacement = velocity × time<br>
s = v × t</p>

<p><strong>Motion with constant acceleration</strong> -<br>
v = u + at<br>
v² = u² + 2as<br>
s = ut + ½ at²<br>
Sₙ = u + a/2 (2n - 1)<br>
Sₙ = displacement in the nth second<br>
Note - Above equations can be used when a = const & u, v, a are directed along the same axis</p>

<p><strong>Motion with varying acceleration</strong> -<br>
Acceleration may be function of displacement, velocity or time.<br>
Equations to be used are,<br>
a = dv/dt and a·ds = v·dv</p>

<p><strong>Examples -</strong></p>

<p><strong>Type 1 - Motion under constant acceleration</strong></p>

<p><strong>Example 1 -</strong><br>
A particle starts with an initial velocity of 2.5 m/s and uniformly accelerates at the rate 0.5 m/s². Determine the displacement in 2 s, time required to attain the velocity of 7.5 m/s and the distance travelled when it attains a velocity of 7.5 m/s.<br>
Solution -<br>
s = ut + ½ at² = 6 m<br>
v = u + at<br>
∴ t = 10 sec<br>
v² = u² + 2as<br>
∴ s = 50 m</p>

<p><strong>Example 2 -</strong><br>
A car comes to complete stop from an initial speed of 50 kmph in a distance of 100 m. With the same constant acceleration, what would be the stopping distance ‘s’ from an initial speed of 70 kmph.<br>
Solution -<br>
v² = u² + 2as<br>
0 = (50 × 5/18)² + 2(a)(100)<br>
∴ a = -0.964 m/s²<br>
v² = u² + 2as<br>
0 = (70 × 5/18)² + 2(-0.964)(s)<br>
∴ s = 196.1 m</p>

<p><strong>Example 3 -</strong><br>
In a flood relief area, a helicopter going up with a constant velocity drops first batch of food packets which takes 4 seconds to reach the ground. No sooner than this batch reaches the ground, second batch of food packets are released which takes 5 seconds to reach the ground. From what height is the first batch of packets released? What is the velocity with which the helicopter is moving up?<br>
Solution -<br>
Using s = ut + ½ at²<br>
s₁ = -u(4) + ½ (9.81)(4)² ... (i)<br>
s₂ = -u(5) + ½ (9.81)(5)² ... (ii)<br>
s₂ = s₁ + 4u ... (iii)<br>
Solving,<br>
u = 8.829 m/s and s₁ = 43.2 m</p>

<p><strong>Example 4 -</strong><br>
Two cars moving in the same direction are 150 m apart; car A being ahead of car B. At this instant, velocity of A is 3 m/s and constant acceleration is 1.2 m/s², while the velocity of car B is 30 m/s and its uniform retardation is 0.6 m/s². How many times do the cars cross each other? Find when and where do they cross with respect to the given position of A.<br>
Solution -<br>
We have,<br>
s<sub>B</sub> = s<sub>A</sub> + 150 ... (i)<br>
s<sub>A</sub> = ut + ½ at² = 3t + 0.6t² ... (ii)<br>
s<sub>B</sub> = 30t - 0.3t² ... (iii)<br>
Put (ii) and (iii) in (i):<br>
30t - 0.3t² = 3t + 0.6t² + 150<br>
∴ 0.9t² - 27t + 150 = 0<br>
Solving,<br>
t₁ = 7.36 s and t₂ = 22.63 s<br>
Thus, cars cross each other twice.<br>
Substitute t₁ and t₂ into (ii):<br>
s<sub>A1</sub> = 54.58 m<br>
s<sub>A2</sub> = 375.16 m</p>

<p><strong>Example 5 -</strong><br>
A bullet moving at a speed of 300 m/s has its speed reduced to 270 m/s when it passes through a board. Determine how many such boards the bullet will penetrate before it stops.<br>
Solution -<br>
v² = u² + 2as<br>
270² = 300² + 2as<br>
∴ s₁ = -8550/a<br>
0 = 300² + 2as₂<br>
∴ s₂ = -45000/a<br>
s₂/s₁ = 5.26</p>

<p><strong>Type 2 - Motion with varying acceleration</strong></p>

<p><strong>Example 6 -</strong><br>
A sphere is fired into a medium with an initial velocity of 27 m/s. If it experiences a deceleration a = -6t² m/s², where t is in seconds, determine distance travelled before it stops.<br>
Solution -<br>
a = -6t² ... (i)<br>
v = -2t³ + C₁ ... (ii)<br>
s = -t⁴/2 + C₁t + C₂ ... (iii)<br>
At t = 0, v = 27 m/s → C₁ = 27<br>
At t = 0, s = 0 → C₂ = 0<br>
v = 0 → t = 2.381 s<br>
s = 48.21 m</p>

<p><strong>Example 7 -</strong><br>
A particle moves along a straight line with an acceleration a = (4t² - 2), where a is in m/s² and t is in s. When t = 0, the particle is at 2 m to the left of the origin and when t = 2 s the particle is at 20 m to the left of the origin. Determine the position of the particle at t = 4 s.<br>
Solution -<br>
a = 4t² - 2 ... (i)<br>
v = (4t³/3) - 2t + C₁ ... (ii)<br>
s = (t⁴/3) - t² + C₁t + C₂ ... (iii)<br>
At t = 0, s₀ = -2 → C₂ = -2<br>
At t = 2 s, s₂ = -20 → C₁ = -9.67<br>
At t = 4 s → s₄ = 28.65 m</p>
`,
                },
              ],
            },
            {
              id: "page-3",
              pageNumber: 3,
              title: "Equations of Motion",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-3",
                  type: "text",
                  order: 1,
                  content: `<h3>Type 3 - Motion Curves</h3>

<p>Diagrams indicating s, v, a as a function of time are called as motion curves.</p>

<div class="diagram-container" style="display: flex; gap: 64px; margin: 40px 0;">
  <img src="/image5.png" alt="Physics Diagram 5 - Motion Curves" class="diagram-image">
</div>

<p>
<strong>2.</strong> Slope of (s–t) dig = ds/dt = v (m/s)<br>
Slope of (v–t) dig = dv/dt = a (m/s²)<br>
Slope of (a–t) dig = da/dt = jerk (m/s³)
</p>

<p>
<strong>3.</strong> Area of (a–t) dig = ▲v = change in velocity<br>
Area of (v–t) dig = ▲s = change in displacement
</p>

<p>
<strong>4.</strong> If acceleration is a function of tⁿ:<br>
Velocity will be f(tⁿ⁺¹) and<br>
Displacement will be f(tⁿ⁺²)
</p>

<hr>

<p><strong>Example 12 -</strong></p>

<p>
Fig below shows v–t diagram for particle assuming that t = 0, s₀ = –8 m<br>
1. Draw a–t diagram<br>
2. Draw s–t diagram<br>
3. Find displacement from t = 0 to t = 10 sec<br>
4. Find distance travelled from t = 0 to t = 10 sec
</p>

<div class="diagram-container" style="display: flex; gap: 64px; margin: 40px 0;">
  <img src="/image6.png" alt="Physics Diagram 6 - v-t diagram" class="diagram-image">
</div>

<p><strong>Solution -</strong><br>
1. a₀–₄ = (20 – 0) / 4 = 5 m/s²<br>
&nbsp;&nbsp;&nbsp; a₄–₆ = 0<br>
&nbsp;&nbsp;&nbsp; a₆–₁₀ = (–20 – 20) / 4 = –10 m/s²
</p>

<p>
3. Displacement (0–10) = s₁₀ – s₀ = 72 – (–8) = 80 m<br>
4. Distance (0–10) = ∑ |▲s| = 40 + 40 + 20 + 20 = 120 m
</p>
`,
                },
              ],
            },
            {
              id: "page-4",
              pageNumber: 4,
              title: "Applications",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-4",
                  type: "text",
                  order: 1,
                  content: `<h3>Type 4 - Dependent Motion of Particles</h3>

<p>
1. Motion of connected bodies is called as <strong>dependent motion</strong>.
</p>

<p>
2. Relation between displacement, velocity and acceleration of connected bodies can be obtained by:
</p>

<ul>
  <li>Law of string</li>
  <li>Method of position vectors</li>
</ul>

<p>
3. <strong>Law of string</strong> –<br>
This method can be used when two blocks are connected by one wire.
</p>

<p>
As per this law: <strong>N₁a₁ = N₂a₂</strong><br>
where:<br>
N₁, N₂ = number of tensions supported by block 1 and block 2 respectively<br>
a₁, a₂ = acceleration of block 1 and block 2 respectively
</p>

<p>
Tension member – <strong>Tie</strong><br>
Compression member – <strong>Strut</strong>
</p>
<div class="diagram-container" style="display: flex; gap: 64px; margin: 40px 0;">
  <img src="/image7.png" alt="Physics Diagram 7 - Law of String" class="diagram-image">
</div>

<hr>

<p>

`,
                },
              ],
            },
          ],
        },
        {
          id: "projectile-motion",
          title: "Projectile motion",
          currentPage: 1,
          pages: [
            {
              id: "page-5",
              pageNumber: 1,
              title: "Projectile Motion Basics",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-5",
                  type: "text",
                  order: 1,
                  content: `<h3>Projectile Motion</h3>
                                    <p>Projectile motion is the motion of an object thrown or projected into the air, subject only to acceleration due to gravity.</p>

                                    <p><strong>Key Characteristics:</strong></p>
                                    <ul>
                                        <li>Two-dimensional motion</li>
                                        <li>Constant horizontal velocity</li>
                                        <li>Uniformly accelerated vertical motion</li>
                                        <li>Parabolic trajectory</li>
                                    </ul>

                                    <p><strong>Components of Projectile Motion:</strong></p>
                                    <p><strong>Horizontal Component:</strong> vₓ = v₀ cos θ</p>
                                    <p><strong>Vertical Component:</strong> vᵧ = v₀ sin θ - gt</p>

                                    <p>Where v₀ is initial velocity, θ is launch angle, and g is acceleration due to gravity.</p>`,
                },
              ],
            },
            {
              id: "page-6",
              pageNumber: 2,
              title: "Trajectory Analysis",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-6",
                  type: "text",
                  order: 1,
                  content: `<h3>Trajectory Analysis</h3>
                                    <p>The path followed by a projectile is called its trajectory, which is always parabolic in nature.</p>

                                    <p><strong>Important Parameters:</strong></p>
                                    <ul>
                                        <li><strong>Range (R):</strong> R = (v₀² sin 2θ)/g</li>
                                        <li><strong>Maximum Height (H):</strong> H = (v₀² sin² θ)/(2g)</li>
                                        <li><strong>Time of Flight (T):</strong> T = (2v₀ sin θ)/g</li>
                                    </ul>

                                    <p><strong>Optimal Launch Angle:</strong></p>
                                    <p>For maximum range on level ground, the optimal launch angle is 45°.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "tangential-normal",
          title: "Tangential and normal components",
          currentPage: 1,
          pages: [
            {
              id: "page-7",
              pageNumber: 1,
              title: "Tangential and Normal Components",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-7",
                  type: "text",
                  order: 1,
                  content: `<h3>Tangential and Normal Components</h3>
                                    <p>In curvilinear motion, acceleration can be resolved into two components:</p>

                                    <p><strong>Tangential Component (aₜ):</strong></p>
                                    <ul>
                                        <li>Acts along the tangent to the path</li>
                                        <li>Responsible for change in speed</li>
                                        <li>aₜ = dv/dt</li>
                                    </ul>

                                    <p><strong>Normal Component (aₙ):</strong></p>
                                    <ul>
                                        <li>Acts perpendicular to the path</li>
                                        <li>Responsible for change in direction</li>
                                        <li>aₙ = v²/ρ (where ρ is radius of curvature)</li>
                                    </ul>

                                    <p>Total acceleration: a = √(aₜ² + aₙ²)</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "motion-fill",
          title: "Motion in polar co-ordinates ",
          currentPage: 1,
          pages: [
            {
              id: "page-8",
              pageNumber: 1,
              title: "Motion in polar co-ordinates",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-8",
                  type: "text",
                  order: 1,
                  content: `<h3>Motion Fill</h3>
                                    <p>Advanced motion analysis techniques and applications.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "kinetics-rectilinear",
          title: "Kinetics of rectilinear motion",
          currentPage: 1,
          pages: [
            {
              id: "page-9",
              pageNumber: 1,
              title: "Kinetics of Rectilinear Motion",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-9",
                  type: "text",
                  order: 1,
                  content: `<h3>Kinetics of Rectilinear Motion</h3>
                                    <p>Study of forces and their effects on rectilinear motion.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "kinetics-curvilinear",
          title: "Kinetics of curvilinear Motion",
          currentPage: 1,
          pages: [
            {
              id: "page-10",
              pageNumber: 1,
              title: "Kinetics of Curvilinear Motion",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-10",
                  type: "text",
                  order: 1,
                  content: `<h3>Kinetics of Curvilinear Motion</h3>
                                    <p>Analysis of forces in curvilinear motion systems.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "work-energy",
          title: "Work energy",
          currentPage: 1,
          pages: [
            {
              id: "page-11",
              pageNumber: 1,
              title: "Work Energy",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-11",
                  type: "text",
                  order: 1,
                  content: `<h3>Work Energy</h3>
                                    <p>Work-energy theorem and its applications in mechanics.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "impulse-momentum",
          title: "Impulse momentum and impact",
          currentPage: 1,
          pages: [
            {
              id: "page-12",
              pageNumber: 1,
              title: "Impulse Momentum and Impact",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-12",
                  type: "text",
                  order: 1,
                  content: `<h3>Impulse Momentum and Impact</h3>
                                    <p>Conservation of momentum and impact analysis.</p>`,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: "statics",
      title: "Statics",
      currentPage: 1,
      expanded: true,
      subTopics: [
        {
          id: "resultant-force-system",
          title: "Resultant of force system",
          currentPage: 1,
          pages: [
            {
              id: "page-13",
              pageNumber: 1,
              title: "Resultant of Force System",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-13",
                  type: "text",
                  order: 1,
                  content: `<h3>Resultant of Force System</h3>
                                    <p>The resultant of a force system is a single force that produces the same effect as the combined action of all forces in the system.</p>

                                    <p><strong>Types of Force Systems:</strong></p>
                                    <ul>
                                        <li><strong>Concurrent Forces:</strong> Forces that meet at a common point</li>
                                        <li><strong>Parallel Forces:</strong> Forces that are parallel to each other</li>
                                        <li><strong>Non-concurrent Forces:</strong> Forces that do not meet at a common point</li>
                                    </ul>

                                    <p><strong>Methods of Finding Resultant:</strong></p>
                                    <ul>
                                        <li>Graphical method (Parallelogram law, Triangle law)</li>
                                        <li>Analytical method (Component method)</li>
                                    </ul>`,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

// Helper function to convert new course structure to flat array for compatibility
// Helper function to remove heading tags from HTML content
const stripHeadingTags = (htmlContent) => {
  // Remove h1-h6 tags and their content entirely to avoid duplication with the main heading
  return htmlContent.replace(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi, "").trim();
};

const convertCourseToFlatStructure = (course) => {
  const flatTopics = [];

  course.topics.forEach((topic) => {
    topic.subTopics.forEach((subTopic) => {
      // Convert all pages for this subtopic
      const pages = subTopic.pages.map((page) => {
        const content = page.contents[0]; // Taking first content for now
        const htmlContent = stripHeadingTags(content.content);

        // Handle both single audio file (audioUrl) and multiple audio files (audioUrls)
        const audioSources =
          page.audioUrls || (page.audioUrl ? [page.audioUrl] : []);

        return {
          id: page.id,
          pageNumber: page.pageNumber,
          title: page.title,
          audioSources: audioSources,
          content: {
            heading: page.title,
            body: [
              {
                id: `content-${page.id}`,
                type: "html",
                content: htmlContent,
              },
            ],
          },
        };
      });

      // Create one entry per subtopic (not per page)
      flatTopics.push({
        id: subTopic.id,
        title: subTopic.title,
        currentPageIndex: 0, // Start with first page
        pages: pages,
        // Keep reference to original structure for navigation
        originalStructure: {
          topicId: topic.id,
          subTopicId: subTopic.id,
          totalPages: subTopic.pages.length,
        },
      });
    });
  });

  return flatTopics;
};

const initialCourseData = convertCourseToFlatStructure(mockCourse);

function App() {
  // Enhanced state management with localStorage persistence
  const [courseTopics, setCourseTopics] = useState(initialCourseData);
  const [activeTopicId, setActiveTopicId] = useState(courseTopics[0]?.id);
  const [textSize, setTextSize] = useLocalStorage(
    STORAGE_KEYS.TEXT_SIZE,
    TEXT_SIZES.NORMAL
  );

  const audioRef = useRef(null);

  // Error handling
  const { errors, addError, removeError } = useErrorHandler();

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);
  const currentPageId = useAudioStore((state) => state.currentPageId);

  // Handle page navigation within a subtopic
  const handlePageNavigation = (topicId, pageNumber) => {
    setCourseTopics((prevTopics) =>
      prevTopics.map((topic) => {
        if (topic.id === topicId) {
          const newPageIndex = pageNumber - 1; // Convert to 0-based index
          if (newPageIndex >= 0 && newPageIndex < topic.pages.length) {
            return { ...topic, currentPageIndex: newPageIndex };
          }
        }
        return topic;
      })
    );
  };

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current) {
      const cleanup = initialize(audioRef, []);
      return cleanup;
    }
  }, [initialize]);

  // Load page playlist when active topic or page changes
  useEffect(() => {
    if (courseTopics.length > 0) {
      const activeTopic = courseTopics.find((t) => t.id === activeTopicId);
      if (activeTopic && activeTopic.pages && activeTopic.pages.length > 0) {
        const currentPage =
          activeTopic.pages[activeTopic.currentPageIndex || 0];
        const pageId = `${activeTopic.id}-page-${currentPage.pageNumber}`;

        if (pageId !== currentPageId) {
          // Load the playlist for the current page
          loadPagePlaylist(pageId, currentPage.audioSources);
        }
      }
    }
  }, [activeTopicId, courseTopics, loadPagePlaylist, currentPageId]);

  const activeTopic = courseTopics.find((topic) => topic.id === activeTopicId);

  // Navigation function for bookmarks
  const handleBookmarkNavigation = useCallback(
    (topicId, pageNumber) => {
      console.log(`📍 Navigating to topic: ${topicId}, page: ${pageNumber}`);

      // First, set the active topic
      setActiveTopicId(topicId);

      // Then navigate to the specific page
      handlePageNavigation(topicId, pageNumber);
    },
    [handlePageNavigation]
  );

  // Get current page data for MainContent
  const getCurrentPageData = () => {
    if (!activeTopic || !activeTopic.pages || activeTopic.pages.length === 0) {
      return null;
    }

    const currentPage = activeTopic.pages[activeTopic.currentPageIndex || 0];
    const pageData = {
      id: `${activeTopic.id}-page-${currentPage.pageNumber}`,
      title: `${activeTopic.title} - ${currentPage.title}`,
      audioSources: currentPage.audioSources,
      content: currentPage.content,
      // Add the real topic ID for highlighting system
      realTopicId: activeTopic.id,
      originalStructure: {
        ...activeTopic.originalStructure,
        pageId: currentPage.id,
        pageNumber: currentPage.pageNumber,
      },
    };

    console.log("📄 getCurrentPageData returning:", pageData);
    console.log("📄 Current page content structure:", currentPage.content);

    return pageData;
  };

  const currentPageData = getCurrentPageData();

  return (
    <ErrorBoundary
      message="Something went wrong with the application. Please refresh the page."
      onError={(error, errorInfo) => {
        console.error("App Error Boundary:", error, errorInfo);
        addError("Application error occurred");
      }}
    >
      <div className="app-container">
        <audio
          ref={audioRef}
          style={{ display: "none" }}
          preload="auto"
          crossOrigin="anonymous"
        />

        <ErrorBoundary message="Course navigation error occurred">
          <CourseStructure
            topics={courseTopics}
            activeTopicId={activeTopicId}
            onSelectTopic={setActiveTopicId}
            courseData={mockCourse}
            onNavigateToPage={handleBookmarkNavigation}
          />
        </ErrorBoundary>

        <ErrorBoundary message="Content display error occurred">
          <MainContent
            topic={
              currentPageData || (courseTopics[0] ? getCurrentPageData() : null)
            }
            textSize={textSize}
          />
        </ErrorBoundary>

        <ErrorBoundary message="Tools panel error occurred">
          <ToolsPanel
            activeTextSize={textSize}
            onSetTextSize={setTextSize}
            currentTopic={activeTopic}
            onPageChange={handlePageNavigation}
          />
        </ErrorBoundary>

        {/* Toast notifications for errors */}
        <ToastContainer
          toasts={errors.map((error) => ({
            id: error.id,
            message: error.message,
            type: "error",
            duration: 5000,
            showCloseButton: true,
          }))}
          onRemoveToast={removeError}
        />
      </div>
    </ErrorBoundary>
  );
}

export default App;
