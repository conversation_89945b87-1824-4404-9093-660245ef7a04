import React, { useCallback, useEffect, useRef, useState } from "react";
import "./App.css";
import CourseStructure from "./components/CourseStructure";
import ErrorBoundary from "./components/ErrorBoundary";
import MainContent from "./components/MainContent";
import ToolsPanel from "./components/ToolsPanel";
import { useAudioStore } from "./stores/audioStore";

import { ToastContainer } from "./components/Toast";
import { useErrorHandler } from "./hooks/useErrorHandler";
import { useLocalStorage } from "./hooks/useLocalStorage";
import { STORAGE_KEYS, TEXT_SIZES } from "./utils/constants";

{
  id: "page-4",
    pageNumber: 4,
      title: "Applications",
        audioUrl: "/audio1.mp3",
          contents: [
            {
              id: "content-4",
              type: "text",
              order: 1,
              content: `<h3>Type 4 - Dependent Motion of Particles</h3>

                                                                                                                                                              <p>
                                                                                                                                                                1. Motion of connected bodies is called as <strong>dependent motion</strong>.
                                                                                                                                                              </p>

                                                                                                                                                              <p>
                                                                                                                                                                2. Relation between displacement, velocity and acceleration of connected bodies can be obtained by:
                                                                                                                                                              </p>

                                                                                                                                                              <ul>
                                                                                                                                                                <li>Law of string</li>
                                                                                                                                                                <li>Method of position vectors</li>
                                                                                                                                                              </ul>

                                                                                                                                                              <p>
                                                                                                                                                                3. <strong>Law of string</strong> –<br>
                                                                                                                                                                  This method can be used when two blocks are connected by one wire.
                                                                                                                                                              </p>

                                                                                                                                                              <p>
                                                                                                                                                                As per this law: <strong>N₁a₁ = N₂a₂</strong><br>
                                                                                                                                                                  where:<br>
                                                                                                                                                                    N₁, N₂ = number of tensions supported by block 1 and block 2 respectively<br>
                                                                                                                                                                      a₁, a₂ = acceleration of block 1 and block 2 respectively
                                                                                                                                                                    </p>

                                                                                                                                                                    <p>
                                                                                                                                                                      Tension member – <strong>Tie</strong><br>
                                                                                                                                                                        Compression member – <strong>Strut</strong>
                                                                                                                                                                    </p>
                                                                                                                                                                    <div class="diagram-container" style="display: flex; gap: 64px; margin: 40px 0;">
                                                                                                                                                                      <img src="/image7.png" alt="Physics Diagram 7 - Law of String" class="diagram-image">
                                                                                                                                                                    </div>


                                                                                                                                                                        <p><strong>Key Characteristics:</strong></p>
                                                                                                                                                                        <ul>
                                                                                                                                                                          <li>Two-dimensional motion</li>
                                                                                                                                                                          <li>Constant horizontal velocity</li>
                                                                                                                                                                          <li>Uniformly accelerated vertical motion</li>
                                                                                                                                                                          <li>Parabolic trajectory</li>
                                                                                                                                                                        const {errors, addError, removeError} = useErrorHandler();

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);
  const currentPageId = useAudioStore((state) => state.currentPageId);

  // Handle page navigation within a subtopic
  const handlePageNavigation = (topicId, pageNumber) => {
                                                                                                                                                                          setCourseTopics((prevTopics) =>
                                                                                                                                                                            prevTopics.map((topic) => {
                                                                                                                                                                              if (topic.id === topicId) {
                                                                                                                                                                                const newPageIndex = pageNumber - 1; // Convert to 0-based index
                                                                                                                                                                                if (newPageIndex >= 0 && newPageIndex < topic.pages.length) {
                                                                                                                                                                                  return { ...topic, currentPageIndex: newPageIndex };
                                                                                                                                                                                }
                                                                                                                                                                              }
                                                                                                                                                                              return topic;
                                                                                                                                                                            })
                                                                                                                                                                          );
  };

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
                                                                                                                                                                        if (AudioContext) {
        const audioContext = new AudioContext();

                                                                                                                                                                        // Resume audio context if suspended
                                                                                                                                                                        if (audioContext.state === "suspended") {
                                                                                                                                                                          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
                                                                                                                                                                          initializeAudioContext();
                                                                                                                                                                        document.removeEventListener("click", handleFirstInteraction);
                                                                                                                                                                        document.removeEventListener("touchstart", handleFirstInteraction);
    };

                                                                                                                                                                        document.addEventListener("click", handleFirstInteraction);
                                                                                                                                                                        document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
                                                                                                                                                                          document.removeEventListener("click", handleFirstInteraction);
                                                                                                                                                                        document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current) {
      const cleanup = initialize(audioRef, []);
                                                                                                                                                                        return cleanup;
    }
  }, [initialize]);

  // Load page playlist when active topic or page changes
  useEffect(() => {
    if (courseTopics.length > 0) {
      const activeTopic = courseTopics.find((t) => t.id === activeTopicId);
      if (activeTopic && activeTopic.pages && activeTopic.pages.length > 0) {
        const currentPage =
                                                                                                                                                                        activeTopic.pages[activeTopic.currentPageIndex || 0];
                                                                                                                                                                        const pageId = `${ activeTopic.id } - page - ${ currentPage.pageNumber }`;

                                                                                                                                                                        if (pageId !== currentPageId) {
                                                                                                                                                                          // Load the playlist for the current page
                                                                                                                                                                          loadPagePlaylist(pageId, currentPage.audioSources);
        }
      }
    }
  }, [activeTopicId, courseTopics, loadPagePlaylist, currentPageId]);

  const activeTopic = courseTopics.find((topic) => topic.id === activeTopicId);

                                                                                                                                                                        // Navigation function for bookmarks
                                                                                                                                                                        const handleBookmarkNavigation = useCallback(
    (topicId, pageNumber) => {
                                                                                                                                                                          console.log(`📍 Navigating to topic: ${ topicId }, page: ${ pageNumber }`);

                                                                                                                                                                        // First, set the active topic
                                                                                                                                                                        setActiveTopicId(topicId);

                                                                                                                                                                        // Then navigate to the specific page
                                                                                                                                                                        handlePageNavigation(topicId, pageNumber);
    },
                                                                                                                                                                        [handlePageNavigation]
                                                                                                                                                                        );

  // Get current page data for MainContent
  const getCurrentPageData = () => {
    if (!activeTopic || !activeTopic.pages || activeTopic.pages.length === 0) {
      return null;
    }

                                                                                                                                                                        const currentPage = activeTopic.pages[activeTopic.currentPageIndex || 0];
                                                                                                                                                                        const pageData = {
                                                                                                                                                                          id: `${ activeTopic.id } - page - ${ currentPage.pageNumber }`,
                                                                                                                                                                        title: `${ activeTopic.title } - ${ currentPage.title }`,
                                                                                                                                                                        audioSources: currentPage.audioSources,
                                                                                                                                                                        content: currentPage.content,
                                                                                                                                                                        // Add the real topic ID for highlighting system
                                                                                                                                                                        realTopicId: activeTopic.id,
                                                                                                                                                                        originalStructure: {
                                                                                                                                                                          ...activeTopic.originalStructure,
                                                                                                                                                                          pageId: currentPage.id,
                                                                                                                                                                        pageNumber: currentPage.pageNumber,
      },
    };

                                                                                                                                                                        console.log("📄 getCurrentPageData returning:", pageData);
                                                                                                                                                                        console.log("📄 Current page content structure:", currentPage.content);

                                                                                                                                                                        return pageData;
  };

                                                                                                                                                                        const currentPageData = getCurrentPageData();

                                                                                                                                                                        return (
                                                                                                                                                                        <ErrorBoundary
                                                                                                                                                                          message="Something went wrong with the application. Please refresh the page."
                                                                                                                                                                          onError={(error, errorInfo) => {
                                                                                                                                                                            console.error("App Error Boundary:", error, errorInfo);
                                                                                                                                                                            addError("Application error occurred");
                                                                                                                                                                          }}
                                                                                                                                                                        >
                                                                                                                                                                          <div className="app-container">
                                                                                                                                                                            <audio
                                                                                                                                                                              ref={audioRef}
                                                                                                                                                                              style={{ display: "none" }}
                                                                                                                                                                              preload="auto"
                                                                                                                                                                              crossOrigin="anonymous"
                                                                                                                                                                            />

                                                                                                                                                                            <ErrorBoundary message="Course navigation error occurred">
                                                                                                                                                                              <CourseStructure
                                                                                                                                                                                topics={courseTopics}
                                                                                                                                                                                activeTopicId={activeTopicId}
                                                                                                                                                                                onSelectTopic={setActiveTopicId}
                                                                                                                                                                                courseData={mockCourse}
                                                                                                                                                                                onNavigateToPage={handleBookmarkNavigation}
                                                                                                                                                                              />
                                                                                                                                                                            </ErrorBoundary>

                                                                                                                                                                            <ErrorBoundary message="Content display error occurred">
                                                                                                                                                                              <MainContent
                                                                                                                                                                                topic={
                                                                                                                                                                                  currentPageData || (courseTopics[0] ? getCurrentPageData() : null)
                                                                                                                                                                                }
                                                                                                                                                                                textSize={textSize}
                                                                                                                                                                              />
                                                                                                                                                                            </ErrorBoundary>

                                                                                                                                                                            <ErrorBoundary message="Tools panel error occurred">
                                                                                                                                                                              <ToolsPanel
                                                                                                                                                                                activeTextSize={textSize}
                                                                                                                                                                                onSetTextSize={setTextSize}
                                                                                                                                                                                currentTopic={activeTopic}
                                                                                                                                                                                onPageChange={handlePageNavigation}
                                                                                                                                                                              />
                                                                                                                                                                            </ErrorBoundary>

                                                                                                                                                                            {/* Toast notifications for errors */}
                                                                                                                                                                            <ToastContainer
                                                                                                                                                                              toasts={errors.map((error) => ({
                                                                                                                                                                                id: error.id,
                                                                                                                                                                                message: error.message,
                                                                                                                                                                                type: "error",
                                                                                                                                                                                duration: 5000,
                                                                                                                                                                                showCloseButton: true,
                                                                                                                                                                              }))}
                                                                                                                                                                              onRemoveToast={removeError}
                                                                                                                                                                            />
                                                                                                                                                                          </div>
                                                                                                                                                                        </ErrorBoundary>
                                                                                                                                                                        );
}

                                                                                                                                                                        export default App;
