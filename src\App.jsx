import React, { useEffect, useRef } from "react";
import "./App.css";
import ErrorBoundary from "./components/ErrorBoundary";
import MainContent from "./components/MainContent";
import ToolsPanel from "./components/ToolsPanel";
import { useAudioStore } from "./stores/audioStore";

import { ToastContainer } from "./components/Toast";
import { useErrorHandler } from "./hooks/useErrorHandler";
import { useLocalStorage } from "./hooks/useLocalStorage";
import { STORAGE_KEYS, TEXT_SIZES } from "./utils/constants";

// Simple PDF data - only keph102.pdf
const pdfData = {
  id: "keph102",
  title: "KEPH 102 - Physics Course",
  pdfUrl: "/keph102.pdf",
  audioSources: ["/audio1.mp3", "/audio2.mp3", "/audio3.mp3", "/audio4.mp3"],
  content: {
    heading: "KEPH 102 - Physics Course Material",
    body: [
      {
        id: "content-1",
        type: "pdf",
        pdfUrl: "/keph102.pdf",
      },
    ],
  },
};

function App() {
  const audioRef = useRef(null);
  const [textSize, setTextSize] = useLocalStorage(STORAGE_KEYS.TEXT_SIZE, TEXT_SIZES.NORMAL);

  const { errors, addError, removeError } = useErrorHandler();

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current) {
      const cleanup = initialize(audioRef, pdfData.audioSources);
      return cleanup;
    }
  }, [initialize]);

  // Load audio playlist for the PDF
  useEffect(() => {
    loadPagePlaylist(pdfData.id, pdfData.audioSources);
  }, [loadPagePlaylist]);



  return (
    <ErrorBoundary
      message="Something went wrong with the application. Please refresh the page."
      onError={(error, errorInfo) => {
        console.error("App Error Boundary:", error, errorInfo);
        addError("Application error occurred");
      }}
    >
      <div className="app-container">
        <audio
          ref={audioRef}
          style={{ display: "none" }}
          preload="auto"
          crossOrigin="anonymous"
        />

        <ErrorBoundary message="Content display error occurred">
          <MainContent
            topic={pdfData}
            textSize={textSize}
          />
        </ErrorBoundary>

        <ErrorBoundary message="Tools panel error occurred">
          <ToolsPanel
            activeTextSize={textSize}
            onSetTextSize={setTextSize}
            currentTopic={pdfData}
            onPageChange={() => { }} // No page navigation needed for single PDF
          />
        </ErrorBoundary>

        {/* Toast notifications for errors */}
        <ToastContainer
          toasts={errors.map((error) => ({
            id: error.id,
            message: error.message,
            type: "error",
            duration: 5000,
            showCloseButton: true,
          }))}
          onRemoveToast={removeError}
        />
      </div>
    </ErrorBoundary>
  );
}

export default App;
