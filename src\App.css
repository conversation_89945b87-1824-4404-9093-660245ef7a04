body {
  margin: 0;
  font-family: "Mont<PERSON><PERSON>", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background-color: #fffbf3;
  color: #333;
}

.app-container {
  display: flex;
  min-height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  /* Fallback for browsers that don't support dvh */
  height: 100vh;
  /* Allow container to adjust when DevTools opens */
  height: 100dvh;
  /* Dynamic viewport height for modern browsers */
  width: 100vw;
}

/* 3-Section Layout */
.app-container>* {
  flex-shrink: 0;
  /* Prevent sections from shrinking */
}

/* Left Sidebar - Course Structure */
.app-container>*:first-child {
  width: 280px;
  min-width: 280px;
  max-width: 280px;
}

/* Center Panel - Main Content */
.app-container>*:nth-child(2) {
  flex: 1;
  min-width: 0;
  /* Allow content to shrink if needed */
}

/* Right Panel - Tools Panel */
.app-container>*:last-child {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
}

/* Web-Highlighter Styles */
.web-highlight-wrap {
  cursor: pointer !important;
  transition: all 0.2s ease;
}

.web-highlight-wrap:hover {
  opacity: 0.8;
}

/* Color-specific highlight styles */
.highlight-yellow {
  background-color: rgba(255, 235, 59, 0.3) !important;
  border-bottom: 2px solid #ffeb3b !important;
}

.highlight-green {
  background-color: rgba(139, 195, 74, 0.3) !important;
  border-bottom: 2px solid #8bc34a !important;
}

.highlight-blue {
  background-color: rgba(144, 202, 249, 0.3) !important;
  border-bottom: 2px solid #90caf9 !important;
}

.highlight-pink {
  background-color: rgba(244, 143, 177, 0.3) !important;
  border-bottom: 2px solid #f48fb1 !important;
}

.highlight-purple {
  background-color: rgba(206, 147, 216, 0.3) !important;
  border-bottom: 2px solid #ce93d8 !important;
}

/* Hover effect for highlights */
.highlight-hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  transform: translateY(-1px) !important;
}

/* Responsive Design */
@media (max-width: 1200px) {

  /* Reduce sidebar widths on smaller screens */
  .app-container>*:first-child {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
  }

  .app-container>*:last-child {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
  }
}

@media (max-width: 1024px) {

  /* Further reduce sidebar widths */
  .app-container>*:first-child {
    width: 220px;
    min-width: 220px;
    max-width: 220px;
  }

  .app-container>*:last-child {
    width: 260px;
    min-width: 260px;
    max-width: 260px;
  }
}

@media (max-width: 768px) {

  /* Stack layout for mobile */
  .app-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .app-container>* {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    flex: none;
  }

  .app-container>*:first-child {
    order: 1;
    height: auto;
  }

  .app-container>*:nth-child(2) {
    order: 2;
    flex: 1;
    min-height: 60vh;
  }

  .app-container>*:last-child {
    order: 3;
    height: auto;
  }
}