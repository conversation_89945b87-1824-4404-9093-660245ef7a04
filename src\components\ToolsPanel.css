.tools-panel-sidebar {
  width: 320px;
  height: 100%;
  background-color: #fffbf3;
  padding: 20px;
  border-left: 1px solid #e5e2d9;
  /* Left partition/border */
  box-sizing: border-box;
  flex-shrink: 0;
  overflow-y: auto;
  position: relative;
  font-family: inherit;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tools-panel-sidebar::-webkit-scrollbar {
  display: none;
}

/* Tool Section Styling */
.tool-section {
  margin-bottom: 24px;
}

.section-header {
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.pagination-controls {
  display: flex;

  align-items: stretch;
  border: 1px solid #d0c9b6;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-controls>*:not(:last-child) {
  border-right: 1px solid #d0c9b6;
}

.pagination-nav-btn,
.pagination-btn {
  all: unset;
  /* Reset all default button styles */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  height: 100%;
  box-sizing: border-box;
}

.pagination-nav-btn {
  font-size: 14px;
}

.pagination-btn.active {
  font-weight: bold;
  background-color: #333;
  color: white;
  border-radius: 0;
  height: 100%;
}

.text-size-controls {
  display: flex;

  border: 1px solid #d0c9b6;
  border-radius: 8px;
  overflow: hidden;
}

.text-size-controls>*:not(:last-child) {
  border-right: 1px solid #d0c9b6;
}

.text-size-btn {
  flex: 1;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 14px;
  color: #333;
  padding: 4px 8px;
}

.text-size-btn.active {
  font-weight: bold;
  background-color: #333;
  color: white;
  /* border-radius: 4px; */
}

/* Wrapper for Page controls and bookmark button */
.page-controls-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Bookmark button */
.add-bookmark-btn {
  width: 38px;
  height: 38px;
  border: 1px solid #d0c9b6;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.add-bookmark-btn:hover {
  background-color: #f8f6f0;
}

.add-bookmark-btn svg {
  width: 20px;
  height: 20px;
}

.media-player {
  border: 1px solid #d0c9b6;
  border-radius: 20px;
  padding: 10px 16px;

  display: flex;
  align-items: center;
  justify-content: space-between;
}

.media-info {
  flex: 1;
  min-width: 0;
}

.media-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.media-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.media-control-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  fill: #333;
  font-size: 18px;
}

.media-control-btn.play-pause {
  width: 14px;
  height: 14px;
}

.media-control-btn:disabled {
  fill: #bbb;
}

.media-control-btn:disabled {
  cursor: not-allowed;
}

/* Audio List */
.audio-list {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #e5e2d9;
  border-radius: 8px;
}

.audio-item {
  padding: 10px 14px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.audio-item:last-child {
  border-bottom: none;
}

.audio-item:hover {
  background-color: #f8f8f8;
}

.audio-item.active {
  font-weight: bold;
  background-color: #f0f4f8;
}

.audio-item-title {
  font-size: 13px;
  font-weight: 500;
}

.audio-playing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-wave {
  display: flex;
  gap: 2px;
  align-items: center;
}

.wave-bar {
  width: 2px;
  height: 12px;
  background-color: #333;
  border-radius: 1px;
  animation: wave 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(2) {
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {

  0%,
  100% {
    height: 4px;
  }

  50% {
    height: 12px;
  }
}

/* Annotate Section with Separator */
.annotate-section.has-separator {
  padding-bottom: 24px;
  margin-bottom: 24px;
}

.annotate-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Highlight Mode Toggle */
.highlight-mode-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

.highlight-mode-btn {
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.highlight-mode-btn:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.highlight-mode-btn.active {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.highlight-mode-btn.active:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* Color Picker Section */
.color-picker-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.color-picker-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  text-align: center;
}

.highlighter-container svg {
  width: 48px;
  height: 48px;
}

.tools-separator {
  border: none;
  border-top: 1px solid #e5e2d9;
  margin: 0 -20px;
}

.textarea-separator {
  border: none;
  border-top: 1px solid #e5e2d9;
  /* margin: 0 -20px; */
  padding: 12px;
}

/* Notes Section - Styled to work with RichTextEditor component */
.notes-section {
  margin-bottom: 0;
}

.notes-section .rich-text-editor-content {
  border: 1px solid #e5e2d9;
  border-radius: 10px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.notes-section .rich-text-editor-content:focus {
  outline: 1px solid #aaa;
}

.notes-section .rich-text-editor-content:empty::before {
  content: "Write your notes here....";
  color: #aaa;
  cursor: text;
  pointer-events: none;
}