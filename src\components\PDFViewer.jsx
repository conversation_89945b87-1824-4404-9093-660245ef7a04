import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { useHighlightStore } from '../stores/highlightStore';
import { useColorStore } from '../stores/colorStore';
import './PDFViewer.css';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const PDFViewer = ({ pdfUrl, pageId, topicId, textSize }) => {
  const [numPages, setNumPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const pageRef = useRef(null);
  const selectionRef = useRef(null);
  
  // Stores
  const { selectedColor } = useColorStore();
  const { 
    addHighlight, 
    getHighlights, 
    removeHighlight,
    isHighlightMode,
    setHighlightMode 
  } = useHighlightStore();

  // Get scale based on text size
  const getScaleForTextSize = useCallback(() => {
    switch (textSize) {
      case 'small': return 0.8;
      case 'large': return 1.4;
      default: return 1.0;
    }
  }, [textSize]);

  useEffect(() => {
    setScale(getScaleForTextSize());
  }, [getScaleForTextSize]);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setIsLoading(false);
    setError(null);
  };

  const onDocumentLoadError = (error) => {
    console.error('PDF load error:', error);
    setError('Failed to load PDF');
    setIsLoading(false);
  };

  // Handle text selection and highlighting
  const handleMouseUp = useCallback(() => {
    if (!isHighlightMode || !selectedColor || !pageRef.current) return;

    const selection = window.getSelection();
    if (!selection.rangeCount || selection.isCollapsed) return;

    try {
      const range = selection.getRangeAt(0);
      const rects = Array.from(range.getClientRects());
      
      if (rects.length === 0) return;

      // Get the PDF page element to calculate relative positions
      const pageElement = pageRef.current.querySelector('.react-pdf__Page');
      if (!pageElement) return;

      const pageRect = pageElement.getBoundingClientRect();
      
      // Convert screen coordinates to PDF coordinates
      const pdfRects = rects.map(rect => ({
        x: (rect.left - pageRect.left) / scale,
        y: (rect.top - pageRect.top) / scale,
        width: rect.width / scale,
        height: rect.height / scale,
      }));

      // Create highlight data with vector positions
      const highlightData = {
        pageNumber: currentPage,
        color: selectedColor,
        rects: pdfRects,
        viewport: {
          width: pageRect.width / scale,
          height: pageRect.height / scale,
          scale: scale
        }
      };

      // Add highlight to store
      addHighlight(pageId, topicId, highlightData);

      // Clear selection
      selection.removeAllRanges();
      
    } catch (error) {
      console.error('Error creating highlight:', error);
    }
  }, [isHighlightMode, selectedColor, currentPage, pageId, topicId, scale, addHighlight]);

  // Render existing highlights
  const renderHighlights = useCallback(() => {
    const highlights = getHighlights(pageId, topicId);
    const currentPageHighlights = highlights.filter(h => h.position.pageNumber === currentPage);

    return currentPageHighlights.map(highlight => (
      <div key={highlight.id} className="pdf-highlights-container">
        {highlight.position.rects.map((rect, index) => (
          <div
            key={`${highlight.id}-${index}`}
            className="pdf-highlight"
            style={{
              position: 'absolute',
              left: `${rect.x * scale}px`,
              top: `${rect.y * scale}px`,
              width: `${rect.width * scale}px`,
              height: `${rect.height * scale}px`,
              backgroundColor: highlight.color.backgroundColor,
              borderBottom: `2px solid ${highlight.color.color}`,
              pointerEvents: 'none',
              zIndex: 1,
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (e.ctrlKey || e.metaKey) {
                // Remove highlight on Ctrl+Click
                removeHighlight(pageId, topicId, highlight.id);
              }
            }}
          />
        ))}
      </div>
    ));
  }, [getHighlights, pageId, topicId, currentPage, scale, removeHighlight]);

  const goToPrevPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(numPages, prev + 1));
  };

  const zoomIn = () => {
    setScale(prev => Math.min(2.0, prev + 0.2));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(0.5, prev - 0.2));
  };

  if (error) {
    return (
      <div className="pdf-error">
        <p>Error loading PDF: {error}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  return (
    <div className="pdf-viewer-container">
      {/* PDF Controls */}
      <div className="pdf-controls">
        <div className="pdf-navigation">
          <button onClick={goToPrevPage} disabled={currentPage <= 1}>
            Previous
          </button>
          <span className="page-info">
            Page {currentPage} of {numPages || '?'}
          </span>
          <button onClick={goToNextPage} disabled={currentPage >= numPages}>
            Next
          </button>
        </div>
        
        <div className="pdf-zoom">
          <button onClick={zoomOut}>Zoom Out</button>
          <span className="zoom-level">{Math.round(scale * 100)}%</span>
          <button onClick={zoomIn}>Zoom In</button>
        </div>

        <div className="highlight-controls">
          <button 
            className={`highlight-toggle ${isHighlightMode ? 'active' : ''}`}
            onClick={() => setHighlightMode(!isHighlightMode)}
          >
            {isHighlightMode ? 'Exit Highlight' : 'Highlight Mode'}
          </button>
        </div>
      </div>

      {/* PDF Document */}
      <div 
        className="pdf-document-container"
        ref={pageRef}
        onMouseUp={handleMouseUp}
      >
        <Document
          file={pdfUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={<div className="pdf-loading">Loading PDF...</div>}
        >
          <div className="pdf-page-wrapper">
            <Page
              pageNumber={currentPage}
              scale={scale}
              renderTextLayer={true}
              renderAnnotationLayer={false}
            />
            {/* Render highlights overlay */}
            <div className="pdf-highlights-overlay">
              {renderHighlights()}
            </div>
          </div>
        </Document>
      </div>
    </div>
  );
};

export default PDFViewer;
