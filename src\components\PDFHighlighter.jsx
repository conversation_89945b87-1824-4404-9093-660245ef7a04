import React, { useState, useCallback, useMemo } from 'react';
import {
  PdfLoader,
  PdfHighlighter,
  Tip,
  Highlight,
  Popup,
  AreaHighlight,
} from 'react-pdf-highlighter';
import { useHighlightStore } from '../stores/highlightStore';
import { useColorStore } from '../stores/colorStore';
import 'react-pdf-highlighter/dist/style.css';
import './PDFHighlighter.css';

const getNextId = () => String(Math.random()).slice(2);

const HighlightPopup = ({ comment }) => {
  return comment.text ? (
    <div className="highlight-popup">
      <div className="highlight-comment">
        {comment.emoji && <span className="comment-emoji">{comment.emoji}</span>}
        <span className="comment-text">{comment.text}</span>
      </div>
    </div>
  ) : null;
};

const PDFHighlighter = ({ pdfUrl, pageId, topicId, textSize }) => {
  const { selectedColor } = useColorStore();
  const { 
    isHighlightMode, 
    getHighlights, 
    addHighlight, 
    updateHighlight,
    removeHighlight 
  } = useHighlightStore();

  // Get highlights for current page/topic
  const highlights = useMemo(() => {
    return getHighlights(pageId, topicId);
  }, [getHighlights, pageId, topicId]);

  // Handle adding new highlights
  const handleAddHighlight = useCallback((highlight) => {
    console.log('Adding highlight:', highlight);
    
    const newHighlight = {
      ...highlight,
      id: getNextId(),
      comment: {
        text: '',
        emoji: '💡',
        color: selectedColor?.id || 'yellow',
        backgroundColor: selectedColor?.backgroundColor || '#ffeb3b',
      }
    };

    addHighlight(pageId, topicId, newHighlight);
  }, [addHighlight, pageId, topicId, selectedColor]);

  // Handle updating highlights (for area highlights)
  const handleUpdateHighlight = useCallback((highlightId, position, content) => {
    console.log('Updating highlight:', highlightId, position, content);
    updateHighlight(pageId, topicId, highlightId, position, content);
  }, [updateHighlight, pageId, topicId]);

  // Handle removing highlights
  const handleRemoveHighlight = useCallback((highlightId) => {
    console.log('Removing highlight:', highlightId);
    removeHighlight(pageId, topicId, highlightId);
  }, [removeHighlight, pageId, topicId]);

  // Scale factor based on text size
  const scaleFactor = useMemo(() => {
    switch (textSize) {
      case 'small': return 0.8;
      case 'large': return 1.2;
      default: return 1.0;
    }
  }, [textSize]);

  if (!pdfUrl) {
    return (
      <div className="pdf-error">
        <p>No PDF URL provided</p>
      </div>
    );
  }

  return (
    <div className={`pdf-highlighter-container ${textSize}`}>
      <PdfLoader url={pdfUrl} beforeLoad={<div className="pdf-loading">Loading PDF...</div>}>
        {(pdfDocument) => (
          <PdfHighlighter
            pdfDocument={pdfDocument}
            enableAreaSelection={(event) => event.altKey}
            onScrollChange={() => {}}
            scrollRef={() => {}}
            onSelectionFinished={(
              position,
              content,
              hideTipAndSelection,
              transformSelection
            ) => (
              <Tip
                onOpen={transformSelection}
                onConfirm={(comment) => {
                  handleAddHighlight({
                    content,
                    position,
                    comment: {
                      text: comment.text || '',
                      emoji: comment.emoji || '💡',
                      color: selectedColor?.id || 'yellow',
                      backgroundColor: selectedColor?.backgroundColor || '#ffeb3b',
                    }
                  });
                  hideTipAndSelection();
                }}
              />
            )}
            highlightTransform={(
              highlight,
              index,
              setTip,
              hideTip,
              viewportToScaled,
              screenshot,
              isScrolledTo
            ) => {
              const isTextHighlight = !highlight.content?.image;

              const component = isTextHighlight ? (
                <Highlight
                  isScrolledTo={isScrolledTo}
                  position={highlight.position}
                  comment={highlight.comment}
                />
              ) : (
                <AreaHighlight
                  isScrolledTo={isScrolledTo}
                  highlight={highlight}
                  onChange={(boundingRect) => {
                    handleUpdateHighlight(
                      highlight.id,
                      { boundingRect: viewportToScaled(boundingRect) },
                      { image: screenshot(boundingRect) }
                    );
                  }}
                />
              );

              return (
                <Popup
                  popupContent={<HighlightPopup comment={highlight.comment} />}
                  onMouseOver={(popupContent) =>
                    setTip(highlight, (highlight) => popupContent)
                  }
                  onMouseOut={hideTip}
                  key={index}
                  children={component}
                />
              );
            }}
            highlights={highlights}
          />
        )}
      </PdfLoader>
    </div>
  );
};

export default PDFHighlighter;
