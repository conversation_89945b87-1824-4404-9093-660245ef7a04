import { create } from 'zustand';

/**
 * Highlight Store for managing PDF text highlights with vector positions
 * Stores only position data, not text content
 */
export const useHighlightStore = create((set, get) => ({
  // State
  highlights: {}, // Structure: { pageId: { topicId: [highlights] } }
  selectedColor: null,
  isHighlightMode: false,

  // Actions
  setSelectedColor: (color) => set({ selectedColor: color }),
  
  setHighlightMode: (enabled) => set({ isHighlightMode: enabled }),

  // Add a new highlight with vector position data
  addHighlight: (pageId, topicId, highlightData) => {
    const { highlights } = get();
    
    const newHighlight = {
      id: `highlight_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      color: highlightData.color,
      // Vector position data (no text content)
      position: {
        pageNumber: highlightData.pageNumber,
        // Bounding rectangles for the highlighted text
        rects: highlightData.rects, // Array of { x, y, width, height }
        // PDF coordinate system data
        viewport: highlightData.viewport, // { width, height, scale }
      },
      // Metadata
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    set((state) => ({
      highlights: {
        ...state.highlights,
        [pageId]: {
          ...state.highlights[pageId],
          [topicId]: [
            ...(state.highlights[pageId]?.[topicId] || []),
            newHighlight
          ]
        }
      }
    }));

    return newHighlight.id;
  },

  // Get highlights for a specific page and topic
  getHighlights: (pageId, topicId) => {
    const { highlights } = get();
    return highlights[pageId]?.[topicId] || [];
  },

  // Remove a highlight by ID
  removeHighlight: (pageId, topicId, highlightId) => {
    set((state) => ({
      highlights: {
        ...state.highlights,
        [pageId]: {
          ...state.highlights[pageId],
          [topicId]: (state.highlights[pageId]?.[topicId] || []).filter(
            h => h.id !== highlightId
          )
        }
      }
    }));
  },

  // Update highlight color
  updateHighlightColor: (pageId, topicId, highlightId, newColor) => {
    set((state) => ({
      highlights: {
        ...state.highlights,
        [pageId]: {
          ...state.highlights[pageId],
          [topicId]: (state.highlights[pageId]?.[topicId] || []).map(h =>
            h.id === highlightId 
              ? { ...h, color: newColor, updatedAt: new Date().toISOString() }
              : h
          )
        }
      }
    }));
  },

  // Clear all highlights for a page/topic
  clearHighlights: (pageId, topicId) => {
    set((state) => ({
      highlights: {
        ...state.highlights,
        [pageId]: {
          ...state.highlights[pageId],
          [topicId]: []
        }
      }
    }));
  },

  // Get all highlights (for export/backup)
  getAllHighlights: () => {
    const { highlights } = get();
    return highlights;
  },

  // Import highlights (for restore/sync)
  importHighlights: (highlightData) => {
    set({ highlights: highlightData });
  },

  // Get highlight statistics
  getHighlightStats: () => {
    const { highlights } = get();
    let totalHighlights = 0;
    const colorCounts = {};

    Object.values(highlights).forEach(pageHighlights => {
      Object.values(pageHighlights).forEach(topicHighlights => {
        topicHighlights.forEach(highlight => {
          totalHighlights++;
          const colorId = highlight.color?.id || 'unknown';
          colorCounts[colorId] = (colorCounts[colorId] || 0) + 1;
        });
      });
    });

    return {
      total: totalHighlights,
      byColor: colorCounts
    };
  }
}));
