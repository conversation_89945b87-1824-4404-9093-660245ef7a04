import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * Highlight Store for managing PDF text highlights using react-pdf-highlighter
 * Compatible with react-pdf-highlighter's IHighlight interface
 */
export const useHighlightStore = create(
  persist(
    (set, get) => ({
      // State
      highlights: {}, // Structure: { pageId: { topicId: [IHighlight[]] } }
      isHighlightMode: true, // Default to highlight mode enabled

      // Actions
      setHighlightMode: (enabled) => set({ isHighlightMode: enabled }),

      // Add a new highlight (react-pdf-highlighter format)
      addHighlight: (pageId, topicId, highlight) => {
        const { highlights } = get();

        set((state) => ({
          highlights: {
            ...state.highlights,
            [pageId]: {
              ...state.highlights[pageId],
              [topicId]: [
                ...(state.highlights[pageId]?.[topicId] || []),
                highlight
              ]
            }
          }
        }));

        return highlight.id;
      },

      // Get highlights for a specific page and topic
      getHighlights: (pageId, topicId) => {
        const { highlights } = get();
        return highlights[pageId]?.[topicId] || [];
      },

      // Update a highlight (react-pdf-highlighter format)
      updateHighlight: (pageId, topicId, highlightId, position, content) => {
        set((state) => ({
          highlights: {
            ...state.highlights,
            [pageId]: {
              ...state.highlights[pageId],
              [topicId]: (state.highlights[pageId]?.[topicId] || []).map(h =>
                h.id === highlightId
                  ? {
                    ...h,
                    position: { ...h.position, ...position },
                    content: { ...h.content, ...content }
                  }
                  : h
              )
            }
          }
        }));
      },

      // Remove a highlight by ID
      removeHighlight: (pageId, topicId, highlightId) => {
        set((state) => ({
          highlights: {
            ...state.highlights,
            [pageId]: {
              ...state.highlights[pageId],
              [topicId]: (state.highlights[pageId]?.[topicId] || []).filter(
                h => h.id !== highlightId
              )
            }
          }
        }));
      },

      // Clear all highlights for a page/topic
      clearHighlights: (pageId, topicId) => {
        set((state) => ({
          highlights: {
            ...state.highlights,
            [pageId]: {
              ...state.highlights[pageId],
              [topicId]: []
            }
          }
        }));
      },

      // Get all highlights (for export/backup)
      getAllHighlights: () => {
        const { highlights } = get();
        return highlights;
      },

      // Import highlights (for restore/sync)
      importHighlights: (highlightData) => {
        set({ highlights: highlightData });
      },

      // Get highlight statistics
      getHighlightStats: () => {
        const { highlights } = get();
        let totalHighlights = 0;
        const colorCounts = {};

        Object.values(highlights).forEach(pageHighlights => {
          Object.values(pageHighlights).forEach(topicHighlights => {
            topicHighlights.forEach(highlight => {
              totalHighlights++;
              const colorKey = highlight.comment?.color || 'default';
              colorCounts[colorKey] = (colorCounts[colorKey] || 0) + 1;
            });
          });
        });

        return {
          total: totalHighlights,
          byColor: colorCounts
        };
      }
    }),
    {
      name: 'pdf-highlights-storage', // localStorage key
      // Only persist highlights, not UI state
      partialize: (state) => ({ highlights: state.highlights }),
    }
  )
);
